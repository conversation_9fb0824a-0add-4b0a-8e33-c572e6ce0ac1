<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Hide App Banner</title>
    <style>
        /* CSS to completely hide the "NOSSO APP" banner */
        
        /* Hide any element containing "NOSSO APP" text */
        *:contains("NOSSO APP") {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }

        /* Hide any element containing "Baixar agora" text */
        *:contains("Baixar agora"),
        *:contains("baixar agora"),
        *:contains("BAIXAR AGORA") {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }

        /* Hide common banner class names */
        .app-banner,
        .download-banner,
        .app-download-banner,
        .mobile-app-banner,
        .promo-banner,
        .banner-app,
        .nosso-app-banner,
        .app-promotion,
        .banner,
        .header-banner,
        .top-banner,
        .promotion-banner {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }

        /* Hide banners with blue background */
        div[style*="background-color: #"],
        div[style*="background: #"],
        div[style*="background-color: var(--ci-primary-color)"],
        div[style*="background-color: var(--primary-color)"],
        div[style*="background-color: blue"],
        div[style*="background-color: #007bff"],
        div[style*="background-color: #0066cc"] {
            display: none !important;
        }

        /* Hide any fixed positioned banners at the top */
        div[style*="position: fixed"][style*="top: 0"],
        div[style*="position: fixed"][style*="z-index"] {
            display: none !important;
        }

        /* Additional selectors for Vue.js generated content */
        [data-v-*]:contains("NOSSO APP"),
        [data-v-*]:contains("Baixar agora") {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
        }

        /* Force hide with high specificity */
        html body div:contains("NOSSO APP"),
        html body div:contains("Baixar agora") {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }
    </style>
</head>
<body>
    <script>
        /**
         * JavaScript to completely remove the "NOSSO APP" banner
         * This script will continuously monitor and remove the banner
         */

        (function() {
            'use strict';
            
            // Function to remove elements containing specific text
            function removeElementsWithText(text) {
                const elements = document.querySelectorAll('*');
                elements.forEach(element => {
                    if (element.textContent && element.textContent.includes(text)) {
                        // Check if it's not the body or html element to avoid removing everything
                        if (element.tagName !== 'BODY' && element.tagName !== 'HTML') {
                            element.remove();
                        }
                    }
                });
            }
            
            // Function to hide elements with specific text
            function hideElementsWithText(text) {
                const elements = document.querySelectorAll('*');
                elements.forEach(element => {
                    if (element.textContent && element.textContent.includes(text)) {
                        if (element.tagName !== 'BODY' && element.tagName !== 'HTML') {
                            element.style.display = 'none';
                            element.style.visibility = 'hidden';
                            element.style.opacity = '0';
                            element.style.height = '0';
                            element.style.overflow = 'hidden';
                        }
                    }
                });
            }
            
            // Function to remove banner elements
            function removeBanner() {
                // Remove elements containing specific text
                removeElementsWithText('NOSSO APP');
                removeElementsWithText('Baixar agora');
                removeElementsWithText('baixar agora');
                removeElementsWithText('BAIXAR AGORA');
                
                // Hide elements as backup
                hideElementsWithText('NOSSO APP');
                hideElementsWithText('Baixar agora');
                hideElementsWithText('baixar agora');
                hideElementsWithText('BAIXAR AGORA');
                
                // Remove common banner classes
                const bannerClasses = [
                    '.app-banner',
                    '.download-banner',
                    '.app-download-banner',
                    '.mobile-app-banner',
                    '.promo-banner',
                    '.banner-app',
                    '.nosso-app-banner',
                    '.app-promotion',
                    '.banner',
                    '.header-banner',
                    '.top-banner',
                    '.promotion-banner'
                ];
                
                bannerClasses.forEach(className => {
                    const elements = document.querySelectorAll(className);
                    elements.forEach(element => element.remove());
                });
                
                // Remove elements with blue background that might be banners
                const allDivs = document.querySelectorAll('div');
                allDivs.forEach(div => {
                    const style = window.getComputedStyle(div);
                    const bgColor = style.backgroundColor;
                    
                    // Check for blue-ish backgrounds
                    if (bgColor.includes('rgb(0, 123, 255)') || 
                        bgColor.includes('#007bff') || 
                        bgColor.includes('#0066cc') ||
                        bgColor.includes('blue')) {
                        
                        // Additional check to see if it contains app-related text
                        if (div.textContent && 
                            (div.textContent.includes('app') || 
                             div.textContent.includes('baixar') || 
                             div.textContent.includes('download'))) {
                            div.remove();
                        }
                    }
                });
            }
            
            // Run immediately
            removeBanner();
            
            // Run when DOM is loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', removeBanner);
            }
            
            // Run when page is fully loaded
            window.addEventListener('load', removeBanner);
            
            // Use MutationObserver to watch for dynamically added content
            const observer = new MutationObserver(function(mutations) {
                let shouldRemove = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // Check if the added node contains banner text
                                if (node.textContent && 
                                    (node.textContent.includes('NOSSO APP') || 
                                     node.textContent.includes('Baixar agora') ||
                                     node.textContent.includes('baixar agora'))) {
                                    shouldRemove = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldRemove) {
                    setTimeout(removeBanner, 100); // Small delay to ensure DOM is updated
                }
            });
            
            // Start observing
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Also run periodically as a fallback
            setInterval(removeBanner, 2000); // Every 2 seconds
            
        })();
    </script>
    
    <p>This file contains CSS and JavaScript to hide the "NOSSO APP" banner. Include the CSS and JavaScript from this file in your main application.</p>
</body>
</html>
