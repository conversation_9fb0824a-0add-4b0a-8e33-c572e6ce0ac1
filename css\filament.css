/**
 * Helpers
 */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

/**
 * Banners
 */

.banner-1 {
    background-color: #EFD259;
    /*min-height: 250px;*/
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    padding: 20px;
}

@media (max-width: 600px) {
    .banner-1 {
        height: 100px;
    }
}

.banner-1 .banner-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    color: #252320;
}

.banner-1 p {
    color: #775107;
}

.banner-1 .banner-img {
    max-width: 250px;
    display: flex;
    align-items: center;
}

@media only screen and (max-width: 600px) {
    .banner-1 .banner-img {
        max-width: 200px;
    }
}

.banner-1 h1 {
    font-weight: 500;
}

.banner-1 p {
    font-size: 0.8rem;
}

/**
 * Carousel
 */
.carousel {
    position: relative;
}

.carousel .carousel-description {
    position: absolute;
    bottom: 15px;
    left: 15px;
}

.carousel .carousel-description p {
    text-transform: uppercase;
    color: #f3f3f3;
    font-size: 0.8rem;
}

.carousel .carousel-description h2 {
    font-weight: bold;
}

.carousel .carousel-description h3 {
    font-size: 0.8rem;
    color: #CCC;
}

.carousel .carousel-item img {
    box-shadow: inset 0 10px 20px rgba(0, 0, 0, 0.4);
    /* Aqui você pode ajustar o deslocamento vertical e o desfoque */
}

.carousel .carousel-controls {
    top: 10px;
    right: 10px;
}

.carousel .carousel-inner .carousel-item img {
    border-radius: 20px;
}

.carousel-item {
    position: relative;
    overflow: hidden;
}

.image-shadow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
    pointer-events: none;
}

:is(.dark .filament-versions-nav-widget) {
    color: white !important;
    border-color: #353333;
}

.show-in-dark {
    display: none;
}

:is(.dark .show-in-dark) {
    display: block;
}

.show-in-light {
    display: block;
}

:is(.dark .show-in-light) {
    display: none;
}