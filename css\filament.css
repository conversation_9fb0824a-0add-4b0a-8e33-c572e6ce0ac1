/**
 * Helpers
 */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

/**
 * Hide App Download Banner - Complete Removal
 */
/* Hide any element containing "NOSSO APP" text */
*:contains("NOSSO APP") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Hide any element containing "Baixar agora" text */
*:contains("Baixar agora"),
*:contains("baixar agora"),
*:contains("BAIXAR AGORA") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Hide common banner class names */
.app-banner,
.download-banner,
.app-download-banner,
.mobile-app-banner,
.promo-banner,
.banner-app,
.nosso-app-banner,
.app-promotion,
.banner,
.header-banner,
.top-banner,
.promotion-banner {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Hide banners with blue background (common for app banners) */
div[style*="background-color: #"],
div[style*="background: #"],
div[style*="background-color: var(--ci-primary-color)"],
div[style*="background-color: var(--primary-color)"],
div[style*="background-color: blue"],
div[style*="background-color: #007bff"],
div[style*="background-color: #0066cc"] {
    display: none !important;
}

/* Hide any fixed positioned banners at the top */
div[style*="position: fixed"][style*="top: 0"],
div[style*="position: fixed"][style*="z-index"] {
    display: none !important;
}

/* Additional selectors for Vue.js generated content */
[data-v-*]:contains("NOSSO APP"),
[data-v-*]:contains("Baixar agora") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Force hide with high specificity */
html body div:contains("NOSSO APP"),
html body div:contains("Baixar agora") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* Hide parent containers that might contain the banner */
.app-container:has(*:contains("NOSSO APP")),
.banner-container:has(*:contains("Baixar agora")) {
    display: none !important;
}

/**
 * Banners
 */

.banner-1 {
    background-color: #EFD259;
    /*min-height: 250px;*/
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    padding: 20px;
}

@media (max-width: 600px) {
    .banner-1 {
        height: 100px;
    }
}

.banner-1 .banner-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    color: #252320;
}

.banner-1 p {
    color: #775107;
}

.banner-1 .banner-img {
    max-width: 250px;
    display: flex;
    align-items: center;
}

@media only screen and (max-width: 600px) {
    .banner-1 .banner-img {
        max-width: 200px;
    }
}

.banner-1 h1 {
    font-weight: 500;
}

.banner-1 p {
    font-size: 0.8rem;
}

/**
 * Carousel
 */
.carousel {
    position: relative;
}

.carousel .carousel-description {
    position: absolute;
    bottom: 15px;
    left: 15px;
}

.carousel .carousel-description p {
    text-transform: uppercase;
    color: #f3f3f3;
    font-size: 0.8rem;
}

.carousel .carousel-description h2 {
    font-weight: bold;
}

.carousel .carousel-description h3 {
    font-size: 0.8rem;
    color: #CCC;
}

.carousel .carousel-item img {
    box-shadow: inset 0 10px 20px rgba(0, 0, 0, 0.4);
    /* Aqui você pode ajustar o deslocamento vertical e o desfoque */
}

.carousel .carousel-controls {
    top: 10px;
    right: 10px;
}

.carousel .carousel-inner .carousel-item img {
    border-radius: 20px;
}

.carousel-item {
    position: relative;
    overflow: hidden;
}

.image-shadow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
    pointer-events: none;
}

:is(.dark .filament-versions-nav-widget) {
    color: white !important;
    border-color: #353333;
}

.show-in-dark {
    display: none;
}

:is(.dark .show-in-dark) {
    display: block;
}

.show-in-light {
    display: block;
}

:is(.dark .show-in-light) {
    display: none;
}
