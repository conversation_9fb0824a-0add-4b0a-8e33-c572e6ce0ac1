/**
 * CSS to completely hide the "NOSSO APP" banner
 * This file contains various selectors to ensure the banner is hidden
 */

/* Hide any element containing "NOSSO APP" text */
*:contains("NOSSO APP") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Hide any element containing "Baixar agora" text */
*:contains("Baixar agora"),
*:contains("baixar agora"),
*:contains("BAIXAR AGORA") {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Hide common banner class names */
.app-banner,
.download-banner,
.app-download-banner,
.mobile-app-banner,
.promo-banner,
.banner-app,
.nosso-app-banner,
.app-promotion {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Hide banners with blue background (common for app banners) */
div[style*="background-color: #"],
div[style*="background: #"],
div[style*="background-color: var(--ci-primary-color)"],
div[style*="background-color: var(--primary-color)"],
div[style*="background-color: blue"],
div[style*="background-color: #007bff"],
div[style*="background-color: #0066cc"] {
    display: none !important;
}

/* Hide any fixed positioned banners at the top */
div[style*="position: fixed"][style*="top: 0"],
div[style*="position: fixed"][style*="z-index"] {
    display: none !important;
}

/* Hide banners with specific text patterns */
div:contains("app"),
div:contains("download"),
div:contains("baixar") {
    display: none !important;
}

/* Additional selectors for Vue.js generated content */
[data-v-*]:contains("NOSSO APP"),
[data-v-*]:contains("Baixar agora") {
    display: none !important;
}

/* Hide any banner-like elements */
.banner,
.header-banner,
.top-banner,
.promotion-banner {
    display: none !important;
}

/* Force hide with high specificity */
html body div:contains("NOSSO APP") {
    display: none !important;
}

html body div:contains("Baixar agora") {
    display: none !important;
}

/* Hide parent containers that might contain the banner */
.app-container:has(*:contains("NOSSO APP")),
.banner-container:has(*:contains("Baixar agora")) {
    display: none !important;
}
